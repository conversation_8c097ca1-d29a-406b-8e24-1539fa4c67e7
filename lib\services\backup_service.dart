import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:my_fincance_app/models/user.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/models/category.dart';
import 'package:my_fincance_app/models/budget.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/models/notification.dart';

class BackupService extends ChangeNotifier {
  final String _userId;

  BackupService(this._userId);

  // Create backup data
  Future<Map<String, dynamic>> createBackup() async {
    try {
      final userBox = Hive.box<User>('users');
      final transactionBox = Hive.box<Transaction>('transactions');
      final categoryBox = Hive.box<Category>('categories');
      final budgetBox = Hive.box<Budget>('budgets');
      final loanBox = Hive.box<Loan>('loans');
      final notificationBox = Hive.box<AppNotification>('notifications');

      // Get user data
      final user = userBox.get(_userId);
      if (user == null) {
        throw Exception('کاربر یافت نشد');
      }

      // Get user's transactions
      final transactions = transactionBox.values
          .where((t) => t.userId == _userId)
          .map((t) => _transactionToMap(t))
          .toList();

      // Get user's categories
      final categories = categoryBox.values
          .where((c) => c.userId == _userId)
          .map((c) => _categoryToMap(c))
          .toList();

      // Get user's budgets
      final budgets = budgetBox.values
          .where((b) => b.userId == _userId)
          .map((b) => _budgetToMap(b))
          .toList();

      // Get user's loans
      final loans = loanBox.values
          .where((l) => l.userId == _userId)
          .map((l) => _loanToMap(l))
          .toList();

      // Get user's notifications
      final notifications = notificationBox.values
          .where((n) => n.userId == _userId)
          .map((n) => _notificationToMap(n))
          .toList();

      final backupData = {
        'version': '1.0',
        'created_at': DateTime.now().toIso8601String(),
        'user_id': _userId,
        'user': _userToMap(user),
        'transactions': transactions,
        'categories': categories,
        'budgets': budgets,
        'loans': loans,
        'notifications': notifications,
        'metadata': {
          'app_version': '1.0.0',
          'platform': Platform.operatingSystem,
          'backup_type': 'full',
        },
      };

      return backupData;
    } catch (e) {
      throw Exception('خطا در ایجاد پشتیبان: ${e.toString()}');
    }
  }

  // Export backup as JSON string
  Future<String> exportBackupAsJson() async {
    final backupData = await createBackup();
    return jsonEncode(backupData);
  }

  // Restore from backup data
  Future<void> restoreFromBackup(Map<String, dynamic> backupData) async {
    try {
      // Validate backup data
      _validateBackupData(backupData);

      final userBox = Hive.box<User>('users');
      final transactionBox = Hive.box<Transaction>('transactions');
      final categoryBox = Hive.box<Category>('categories');
      final budgetBox = Hive.box<Budget>('budgets');
      final loanBox = Hive.box<Loan>('loans');
      final notificationBox = Hive.box<AppNotification>('notifications');

      // Clear existing data for this user
      await _clearUserData();

      // Restore user
      final userData = backupData['user'] as Map<String, dynamic>;
      final user = _mapToUser(userData);
      await userBox.put(_userId, user);

      // Restore categories
      final categoriesData = backupData['categories'] as List<dynamic>;
      for (final categoryData in categoriesData) {
        final category = _mapToCategory(categoryData as Map<String, dynamic>);
        await categoryBox.put(category.id, category);
      }

      // Restore loans
      final loansData = backupData['loans'] as List<dynamic>;
      for (final loanData in loansData) {
        final loan = _mapToLoan(loanData as Map<String, dynamic>);
        await loanBox.put(loan.id, loan);
      }

      // Restore budgets
      final budgetsData = backupData['budgets'] as List<dynamic>;
      for (final budgetData in budgetsData) {
        final budget = _mapToBudget(budgetData as Map<String, dynamic>);
        await budgetBox.put(budget.id, budget);
      }

      // Restore transactions
      final transactionsData = backupData['transactions'] as List<dynamic>;
      for (final transactionData in transactionsData) {
        final transaction = _mapToTransaction(transactionData as Map<String, dynamic>);
        await transactionBox.put(transaction.id, transaction);
      }

      // Restore notifications (optional)
      if (backupData.containsKey('notifications')) {
        final notificationsData = backupData['notifications'] as List<dynamic>;
        for (final notificationData in notificationsData) {
          final notification = _mapToNotification(notificationData as Map<String, dynamic>);
          await notificationBox.put(notification.id, notification);
        }
      }

      notifyListeners();
    } catch (e) {
      throw Exception('خطا در بازیابی اطلاعات: ${e.toString()}');
    }
  }

  // Restore from JSON string
  Future<void> restoreFromJson(String jsonString) async {
    try {
      final backupData = jsonDecode(jsonString) as Map<String, dynamic>;
      await restoreFromBackup(backupData);
    } catch (e) {
      throw Exception('خطا در تجزیه فایل پشتیبان: ${e.toString()}');
    }
  }

  // Clear user data
  Future<void> _clearUserData() async {
    final transactionBox = Hive.box<Transaction>('transactions');
    final categoryBox = Hive.box<Category>('categories');
    final budgetBox = Hive.box<Budget>('budgets');
    final loanBox = Hive.box<Loan>('loans');
    final notificationBox = Hive.box<AppNotification>('notifications');

    // Remove user's transactions
    final transactionKeys = transactionBox.values
        .where((t) => t.userId == _userId)
        .map((t) => t.id)
        .toList();
    for (final key in transactionKeys) {
      await transactionBox.delete(key);
    }

    // Remove user's categories
    final categoryKeys = categoryBox.values
        .where((c) => c.userId == _userId)
        .map((c) => c.id)
        .toList();
    for (final key in categoryKeys) {
      await categoryBox.delete(key);
    }

    // Remove user's budgets
    final budgetKeys = budgetBox.values
        .where((b) => b.userId == _userId)
        .map((b) => b.id)
        .toList();
    for (final key in budgetKeys) {
      await budgetBox.delete(key);
    }

    // Remove user's loans
    final loanKeys = loanBox.values
        .where((l) => l.userId == _userId)
        .map((l) => l.id)
        .toList();
    for (final key in loanKeys) {
      await loanBox.delete(key);
    }

    // Remove user's notifications
    final notificationKeys = notificationBox.values
        .where((n) => n.userId == _userId)
        .map((n) => n.id)
        .toList();
    for (final key in notificationKeys) {
      await notificationBox.delete(key);
    }
  }

  // Validate backup data structure
  void _validateBackupData(Map<String, dynamic> data) {
    final requiredFields = ['version', 'created_at', 'user_id', 'user', 'transactions', 'categories', 'budgets', 'loans'];
    
    for (final field in requiredFields) {
      if (!data.containsKey(field)) {
        throw Exception('فایل پشتیبان ناقص است: فیلد $field یافت نشد');
      }
    }

    if (data['user_id'] != _userId) {
      throw Exception('این پشتیبان متعلق به کاربر دیگری است');
    }

    // Check version compatibility
    final version = data['version'] as String;
    if (!_isVersionCompatible(version)) {
      throw Exception('نسخه فایل پشتیبان پشتیبانی نمی‌شود');
    }
  }

  bool _isVersionCompatible(String version) {
    // For now, only support version 1.0
    return version == '1.0';
  }

  // Get backup statistics
  Future<BackupStats> getBackupStats() async {
    final userBox = Hive.box<User>('users');
    final transactionBox = Hive.box<Transaction>('transactions');
    final categoryBox = Hive.box<Category>('categories');
    final budgetBox = Hive.box<Budget>('budgets');
    final loanBox = Hive.box<Loan>('loans');

    final transactionCount = transactionBox.values.where((t) => t.userId == _userId).length;
    final categoryCount = categoryBox.values.where((c) => c.userId == _userId).length;
    final budgetCount = budgetBox.values.where((b) => b.userId == _userId).length;
    final loanCount = loanBox.values.where((l) => l.userId == _userId).length;

    return BackupStats(
      transactionCount: transactionCount,
      categoryCount: categoryCount,
      budgetCount: budgetCount,
      loanCount: loanCount,
      lastBackupDate: null, // This would be stored separately
    );
  }

  // Helper methods for converting models to/from maps
  Map<String, dynamic> _userToMap(User user) {
    return {
      'email': user.email,
      'name': user.name,
      // Note: password is not included in backup for security
    };
  }

  User _mapToUser(Map<String, dynamic> map) {
    return User()
      ..email = map['email']
      ..name = map['name']
      ..password = ''; // Will need to be set separately
  }

  Map<String, dynamic> _transactionToMap(Transaction transaction) {
    return {
      'id': transaction.id,
      'date': transaction.date.toIso8601String(),
      'description': transaction.description,
      'amount': transaction.amount,
      'type': transaction.type,
      'categoryId': transaction.categoryId,
      'loanId': transaction.loanId,
      'userId': transaction.userId,
    };
  }

  Transaction _mapToTransaction(Map<String, dynamic> map) {
    return Transaction()
      ..id = map['id']
      ..date = DateTime.parse(map['date'])
      ..description = map['description']
      ..amount = map['amount'].toDouble()
      ..type = map['type']
      ..categoryId = map['categoryId']
      ..loanId = map['loanId']
      ..userId = map['userId'];
  }

  Map<String, dynamic> _categoryToMap(Category category) {
    return {
      'id': category.id,
      'name': category.name,
      'type': category.type,
      'userId': category.userId,
    };
  }

  Category _mapToCategory(Map<String, dynamic> map) {
    return Category()
      ..id = map['id']
      ..name = map['name']
      ..type = map['type']
      ..userId = map['userId'];
  }

  Map<String, dynamic> _budgetToMap(Budget budget) {
    return {
      'id': budget.id,
      'period': budget.period.toIso8601String(),
      'amount': budget.amount,
      'categoryId': budget.categoryId,
      'userId': budget.userId,
    };
  }

  Budget _mapToBudget(Map<String, dynamic> map) {
    return Budget()
      ..id = map['id']
      ..period = DateTime.parse(map['period'])
      ..amount = map['amount'].toDouble()
      ..categoryId = map['categoryId']
      ..userId = map['userId'];
  }

  Map<String, dynamic> _loanToMap(Loan loan) {
    return {
      'id': loan.id,
      'name': loan.name,
      'type': loan.type,
      'person': loan.person,
      'initialAmount': loan.initialAmount,
      'startDate': loan.startDate.toIso8601String(),
      'status': loan.status,
      'userId': loan.userId,
    };
  }

  Loan _mapToLoan(Map<String, dynamic> map) {
    return Loan()
      ..id = map['id']
      ..name = map['name']
      ..type = map['type']
      ..person = map['person']
      ..initialAmount = map['initialAmount'].toDouble()
      ..startDate = DateTime.parse(map['startDate'])
      ..status = map['status']
      ..userId = map['userId'];
  }

  Map<String, dynamic> _notificationToMap(AppNotification notification) {
    return {
      'id': notification.id,
      'title': notification.title,
      'message': notification.message,
      'type': notification.type,
      'createdAt': notification.createdAt.toIso8601String(),
      'isRead': notification.isRead,
      'userId': notification.userId,
      'relatedId': notification.relatedId,
      'actionType': notification.actionType,
      'scheduledFor': notification.scheduledFor?.toIso8601String(),
      'isActive': notification.isActive,
    };
  }

  AppNotification _mapToNotification(Map<String, dynamic> map) {
    return AppNotification()
      ..id = map['id']
      ..title = map['title']
      ..message = map['message']
      ..type = map['type']
      ..createdAt = DateTime.parse(map['createdAt'])
      ..isRead = map['isRead']
      ..userId = map['userId']
      ..relatedId = map['relatedId']
      ..actionType = map['actionType']
      ..scheduledFor = map['scheduledFor'] != null ? DateTime.parse(map['scheduledFor']) : null
      ..isActive = map['isActive'] ?? true;
  }
}

class BackupStats {
  final int transactionCount;
  final int categoryCount;
  final int budgetCount;
  final int loanCount;
  final DateTime? lastBackupDate;

  BackupStats({
    required this.transactionCount,
    required this.categoryCount,
    required this.budgetCount,
    required this.loanCount,
    this.lastBackupDate,
  });
}
