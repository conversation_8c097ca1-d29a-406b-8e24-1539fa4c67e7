import 'package:flutter/foundation.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/models/budget.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/services/transaction_service.dart';
import 'package:my_fincance_app/services/budget_service.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:my_fincance_app/services/category_service.dart';

class ReportService extends ChangeNotifier {
  final TransactionService _transactionService;
  final BudgetService _budgetService;
  final LoanService _loanService;
  final CategoryService _categoryService;

  ReportService(
    this._transactionService,
    this._budgetService,
    this._loanService,
    this._categoryService,
  );

  // Monthly Report Data
  MonthlyReport generateMonthlyReport(DateTime month) {
    final startDate = DateTime(month.year, month.month, 1);
    final endDate = DateTime(month.year, month.month + 1, 0);

    final monthTransactions = _transactionService.transactions
        .where((t) => t.date.isAfter(startDate.subtract(const Duration(days: 1))) &&
                     t.date.isBefore(endDate.add(const Duration(days: 1))))
        .toList();

    final income = monthTransactions
        .where((t) => t.type == 'درآمد')
        .fold<double>(0.0, (sum, t) => sum + t.amount);

    final expenses = monthTransactions
        .where((t) => t.type == 'مصرف')
        .fold<double>(0.0, (sum, t) => sum + t.amount);

    final savings = income - expenses;

    // Category breakdown
    final categoryBreakdown = <String, CategoryReportData>{};
    for (final transaction in monthTransactions) {
      final categoryId = transaction.categoryId;
      if (!categoryBreakdown.containsKey(categoryId)) {
        categoryBreakdown[categoryId] = CategoryReportData(
          categoryId: categoryId,
          categoryName: _getCategoryName(categoryId),
          totalAmount: 0,
          transactionCount: 0,
          transactions: [],
        );
      }
      categoryBreakdown[categoryId]!.totalAmount += transaction.amount;
      categoryBreakdown[categoryId]!.transactionCount++;
      categoryBreakdown[categoryId]!.transactions.add(transaction);
    }

    // Budget performance
    final budgets = _budgetService.budgets
        .where((b) => b.period.year == month.year && b.period.month == month.month)
        .toList();

    final budgetPerformance = budgets.map((budget) {
      final actualSpending = _budgetService.getActualSpending(budget.categoryId, budget.period);
      return BudgetPerformanceData(
        budget: budget,
        actualSpending: actualSpending,
        variance: actualSpending - budget.amount,
        categoryName: _getCategoryName(budget.categoryId),
      );
    }).toList();

    return MonthlyReport(
      month: month,
      totalIncome: income,
      totalExpenses: expenses,
      netSavings: savings,
      transactionCount: monthTransactions.length,
      categoryBreakdown: categoryBreakdown.values.toList(),
      budgetPerformance: budgetPerformance,
    );
  }

  // Yearly Report Data
  YearlyReport generateYearlyReport(int year) {
    final monthlyReports = <MonthlyReport>[];
    
    for (int month = 1; month <= 12; month++) {
      final monthDate = DateTime(year, month, 1);
      if (monthDate.isBefore(DateTime.now()) || monthDate.month == DateTime.now().month) {
        monthlyReports.add(generateMonthlyReport(monthDate));
      }
    }

    final totalIncome = monthlyReports.fold<double>(0.0, (sum, report) => sum + report.totalIncome);
    final totalExpenses = monthlyReports.fold<double>(0.0, (sum, report) => sum + report.totalExpenses);
    final totalSavings = totalIncome - totalExpenses;

    // Find best and worst months
    MonthlyReport? bestMonth;
    MonthlyReport? worstMonth;
    
    for (final report in monthlyReports) {
      if (bestMonth == null || report.netSavings > bestMonth.netSavings) {
        bestMonth = report;
      }
      if (worstMonth == null || report.netSavings < worstMonth.netSavings) {
        worstMonth = report;
      }
    }

    return YearlyReport(
      year: year,
      monthlyReports: monthlyReports,
      totalIncome: totalIncome,
      totalExpenses: totalExpenses,
      totalSavings: totalSavings,
      averageMonthlyIncome: monthlyReports.isNotEmpty ? totalIncome / monthlyReports.length : 0,
      averageMonthlyExpenses: monthlyReports.isNotEmpty ? totalExpenses / monthlyReports.length : 0,
      bestMonth: bestMonth,
      worstMonth: worstMonth,
    );
  }

  // Loan Report Data
  LoanReport generateLoanReport() {
    final allLoans = _loanService.loans;
    final activeLoans = _loanService.activeLoans;
    final completedLoans = allLoans.where((l) => l.status == 'تمام شده').toList();

    final totalDebtAmount = allLoans
        .where((l) => l.type == 'بدهی')
        .fold<double>(0.0, (sum, l) => sum + l.initialAmount);

    final totalCreditAmount = allLoans
        .where((l) => l.type == 'طلب')
        .fold<double>(0.0, (sum, l) => sum + l.initialAmount);

    final activeDebtBalance = _loanService.getTotalActiveDebt();
    final activeCreditBalance = _loanService.getTotalActiveCredit();

    final loanDetails = allLoans.map((loan) {
      final remainingBalance = _loanService.getRemainingBalance(loan.id);
      final progress = _loanService.getLoanProgress(loan.id);
      final relatedTransactions = _transactionService.getTransactionsForLoan(loan.id);

      return LoanReportData(
        loan: loan,
        remainingBalance: remainingBalance,
        paidAmount: loan.initialAmount - remainingBalance,
        progress: progress,
        transactionCount: relatedTransactions.length,
        lastPaymentDate: relatedTransactions.isNotEmpty 
            ? relatedTransactions.map((t) => t.date).reduce((a, b) => a.isAfter(b) ? a : b)
            : null,
      );
    }).toList();

    return LoanReport(
      totalLoans: allLoans.length,
      activeLoans: activeLoans.length,
      completedLoans: completedLoans.length,
      totalDebtAmount: totalDebtAmount,
      totalCreditAmount: totalCreditAmount,
      activeDebtBalance: activeDebtBalance,
      activeCreditBalance: activeCreditBalance,
      loanDetails: loanDetails,
    );
  }

  // Category Analysis
  List<CategoryAnalysis> generateCategoryAnalysis(DateTime startDate, DateTime endDate) {
    final transactions = _transactionService.transactions
        .where((t) => t.date.isAfter(startDate.subtract(const Duration(days: 1))) &&
                     t.date.isBefore(endDate.add(const Duration(days: 1))))
        .toList();

    final categoryMap = <String, List<Transaction>>{};
    for (final transaction in transactions) {
      if (!categoryMap.containsKey(transaction.categoryId)) {
        categoryMap[transaction.categoryId] = [];
      }
      categoryMap[transaction.categoryId]!.add(transaction);
    }

    return categoryMap.entries.map((entry) {
      final categoryTransactions = entry.value;
      final totalAmount = categoryTransactions.fold<double>(0.0, (sum, t) => sum + t.amount);
      final averageAmount = totalAmount / categoryTransactions.length;
      
      final incomeTransactions = categoryTransactions.where((t) => t.type == 'درآمد').toList();
      final expenseTransactions = categoryTransactions.where((t) => t.type == 'مصرف').toList();

      return CategoryAnalysis(
        categoryId: entry.key,
        categoryName: _getCategoryName(entry.key),
        totalAmount: totalAmount,
        transactionCount: categoryTransactions.length,
        averageAmount: averageAmount,
        incomeAmount: incomeTransactions.fold<double>(0.0, (sum, t) => sum + t.amount),
        expenseAmount: expenseTransactions.fold<double>(0.0, (sum, t) => sum + t.amount),
        firstTransactionDate: categoryTransactions.map((t) => t.date).reduce((a, b) => a.isBefore(b) ? a : b),
        lastTransactionDate: categoryTransactions.map((t) => t.date).reduce((a, b) => a.isAfter(b) ? a : b),
      );
    }).toList()..sort((a, b) => b.totalAmount.compareTo(a.totalAmount));
  }

  String _getCategoryName(String categoryId) {
    try {
      final category = _categoryService.categories.firstWhere((c) => c.id == categoryId);
      return category.name;
    } catch (e) {
      return 'نامشخص';
    }
  }

  // Export data as CSV-like string
  String exportMonthlyReportAsText(MonthlyReport report) {
    final buffer = StringBuffer();
    buffer.writeln('گزارش ماهانه - ${report.month.year}/${report.month.month}');
    buffer.writeln('=====================================');
    buffer.writeln('مجموع درآمد: ${report.totalIncome.toStringAsFixed(0)} افغانی');
    buffer.writeln('مجموع مصارف: ${report.totalExpenses.toStringAsFixed(0)} افغانی');
    buffer.writeln('پس‌انداز خالص: ${report.netSavings.toStringAsFixed(0)} افغانی');
    buffer.writeln('تعداد تراکنش‌ها: ${report.transactionCount}');
    buffer.writeln('');
    
    buffer.writeln('تفکیک دسته‌بندی‌ها:');
    buffer.writeln('---------------------');
    for (final category in report.categoryBreakdown) {
      buffer.writeln('${category.categoryName}: ${category.totalAmount.toStringAsFixed(0)} افغانی (${category.transactionCount} تراکنش)');
    }
    
    return buffer.toString();
  }
}

// Data Models for Reports
class MonthlyReport {
  final DateTime month;
  final double totalIncome;
  final double totalExpenses;
  final double netSavings;
  final int transactionCount;
  final List<CategoryReportData> categoryBreakdown;
  final List<BudgetPerformanceData> budgetPerformance;

  MonthlyReport({
    required this.month,
    required this.totalIncome,
    required this.totalExpenses,
    required this.netSavings,
    required this.transactionCount,
    required this.categoryBreakdown,
    required this.budgetPerformance,
  });
}

class YearlyReport {
  final int year;
  final List<MonthlyReport> monthlyReports;
  final double totalIncome;
  final double totalExpenses;
  final double totalSavings;
  final double averageMonthlyIncome;
  final double averageMonthlyExpenses;
  final MonthlyReport? bestMonth;
  final MonthlyReport? worstMonth;

  YearlyReport({
    required this.year,
    required this.monthlyReports,
    required this.totalIncome,
    required this.totalExpenses,
    required this.totalSavings,
    required this.averageMonthlyIncome,
    required this.averageMonthlyExpenses,
    this.bestMonth,
    this.worstMonth,
  });
}

class CategoryReportData {
  final String categoryId;
  final String categoryName;
  double totalAmount;
  int transactionCount;
  final List<Transaction> transactions;

  CategoryReportData({
    required this.categoryId,
    required this.categoryName,
    required this.totalAmount,
    required this.transactionCount,
    required this.transactions,
  });
}

class BudgetPerformanceData {
  final Budget budget;
  final double actualSpending;
  final double variance;
  final String categoryName;

  BudgetPerformanceData({
    required this.budget,
    required this.actualSpending,
    required this.variance,
    required this.categoryName,
  });

  double get performancePercentage => budget.amount > 0 ? (actualSpending / budget.amount) * 100 : 0;
  bool get isOverBudget => variance > 0;
}

class LoanReport {
  final int totalLoans;
  final int activeLoans;
  final int completedLoans;
  final double totalDebtAmount;
  final double totalCreditAmount;
  final double activeDebtBalance;
  final double activeCreditBalance;
  final List<LoanReportData> loanDetails;

  LoanReport({
    required this.totalLoans,
    required this.activeLoans,
    required this.completedLoans,
    required this.totalDebtAmount,
    required this.totalCreditAmount,
    required this.activeDebtBalance,
    required this.activeCreditBalance,
    required this.loanDetails,
  });
}

class LoanReportData {
  final Loan loan;
  final double remainingBalance;
  final double paidAmount;
  final double progress;
  final int transactionCount;
  final DateTime? lastPaymentDate;

  LoanReportData({
    required this.loan,
    required this.remainingBalance,
    required this.paidAmount,
    required this.progress,
    required this.transactionCount,
    this.lastPaymentDate,
  });
}

class CategoryAnalysis {
  final String categoryId;
  final String categoryName;
  final double totalAmount;
  final int transactionCount;
  final double averageAmount;
  final double incomeAmount;
  final double expenseAmount;
  final DateTime firstTransactionDate;
  final DateTime lastTransactionDate;

  CategoryAnalysis({
    required this.categoryId,
    required this.categoryName,
    required this.totalAmount,
    required this.transactionCount,
    required this.averageAmount,
    required this.incomeAmount,
    required this.expenseAmount,
    required this.firstTransactionDate,
    required this.lastTransactionDate,
  });

  double get percentageOfTotal => totalAmount;
  bool get isIncomeCategory => incomeAmount > expenseAmount;
}
