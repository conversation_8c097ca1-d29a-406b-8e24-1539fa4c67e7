import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:my_fincance_app/models/budget.dart';
import 'package:my_fincance_app/models/category.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/models/notification.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/models/user.dart';
import 'package:my_fincance_app/pages/auth/login_page.dart';
import 'package:my_fincance_app/pages/home_page.dart';
import 'package:my_fincance_app/services/auth_service.dart';
import 'package:my_fincance_app/services/budget_service.dart';
import 'package:my_fincance_app/services/capital_service.dart';
import 'package:my_fincance_app/services/category_service.dart';
import 'package:my_fincance_app/services/notification_service.dart';
import 'package:my_fincance_app/services/report_service.dart';
import 'package:my_fincance_app/services/transaction_service.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:provider/provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Hive.initFlutter();

  Hive.registerAdapter(UserAdapter());
  Hive.registerAdapter(LoanAdapter());
  Hive.registerAdapter(CategoryAdapter());
  Hive.registerAdapter(TransactionAdapter());
  Hive.registerAdapter(BudgetAdapter());
  Hive.registerAdapter(AppNotificationAdapter());

  await Hive.openBox<User>('users');
  await Hive.openBox<Loan>('loans');
  await Hive.openBox<Category>('categories');
  await Hive.openBox<Transaction>('transactions');
  await Hive.openBox<Budget>('budgets');
  await Hive.openBox<AppNotification>('notifications');

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => AuthService()),
        ChangeNotifierProxyProvider<AuthService, CategoryService>(
          create: (context) => CategoryService(''),
          update: (context, authService, categoryService) {
            final userId = authService.currentUserId;
            return CategoryService(userId);
          },
        ),
        ChangeNotifierProxyProvider<AuthService, LoanService>(
          create: (context) => LoanService(''),
          update: (context, authService, loanService) {
            final userId = authService.currentUserId;
            return LoanService(userId);
          },
        ),
        ChangeNotifierProxyProvider2<AuthService, LoanService, TransactionService>(
          create: (context) => TransactionService(''),
          update: (context, authService, loanService, transactionService) {
            final userId = authService.currentUserId;
            final newTransactionService = TransactionService(userId);
            newTransactionService.setLoanService(loanService);
            return newTransactionService;
          },
        ),
        ChangeNotifierProxyProvider<AuthService, BudgetService>(
          create: (context) => BudgetService(''),
          update: (context, authService, budgetService) {
            final userId = authService.currentUserId;
            return BudgetService(userId);
          },
        ),
        ChangeNotifierProxyProvider2<TransactionService, LoanService, CapitalService>(
          create: (context) => CapitalService(
            TransactionService(''),
            LoanService(''),
          ),
          update: (context, transactionService, loanService, capitalService) {
            return CapitalService(transactionService, loanService);
          },
        ),
        ChangeNotifierProxyProvider<AuthService, NotificationService>(
          create: (context) => NotificationService(''),
          update: (context, authService, notificationService) {
            final userId = authService.currentUserId;
            return NotificationService(userId);
          },
        ),
        ChangeNotifierProxyProvider4<TransactionService, BudgetService, LoanService, CategoryService, ReportService>(
          create: (context) => ReportService(
            TransactionService(''),
            BudgetService(''),
            LoanService(''),
            CategoryService(''),
          ),
          update: (context, transactionService, budgetService, loanService, categoryService, reportService) {
            return ReportService(transactionService, budgetService, loanService, categoryService);
          },
        ),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'مرکز مالی من',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: Colors.blue.shade600,
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue.shade600,
          brightness: Brightness.light,
        ),
        textTheme: GoogleFonts.vazirmatnTextTheme(
          Theme.of(context).textTheme,
        ).apply(
          bodyColor: Colors.grey.shade800,
          displayColor: Colors.grey.shade900,
        ),
        appBarTheme: AppBarTheme(
          backgroundColor: Colors.blue.shade50,
          foregroundColor: Colors.blue.shade800,
          elevation: 0,
          centerTitle: true,
          titleTextStyle: GoogleFonts.vazirmatn(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.blue.shade800,
          ),
        ),
        cardTheme: CardTheme(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: const EdgeInsets.symmetric(vertical: 4),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            elevation: 2,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.blue.shade600, width: 2),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        bottomNavigationBarTheme: BottomNavigationBarThemeData(
          backgroundColor: Colors.white,
          selectedItemColor: Colors.blue.shade600,
          unselectedItemColor: Colors.grey.shade500,
          type: BottomNavigationBarType.fixed,
          elevation: 8,
        ),
      ),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('fa', ''), // Persian
      ],
      locale: const Locale('fa', ''),
      home: Consumer<AuthService>(
        builder: (context, authService, child) {
          // Auto-login for single user app
          if (authService.isFirstTime || !authService.isLoggedIn) {
            authService.autoLogin();
          }

          return authService.isLoggedIn ? const HomePage() : const LoginPage();
        },
      ),
    );
  }
}