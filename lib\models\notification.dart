import 'package:hive/hive.dart';

part 'notification.g.dart';

@HiveType(typeId: 5)
class AppNotification extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String title;

  @HiveField(2)
  late String message;

  @HiveField(3)
  late String type; // 'budget_exceeded', 'loan_reminder', 'payment_due', 'financial_tip'

  @HiveField(4)
  late DateTime createdAt;

  @HiveField(5)
  late bool isRead;

  @HiveField(6)
  late String userId;

  @HiveField(7)
  String? relatedId; // ID of related loan, budget, etc.

  @HiveField(8)
  String? actionType; // 'view_loan', 'view_budget', 'add_transaction'

  @HiveField(9)
  DateTime? scheduledFor; // For future notifications

  @HiveField(10)
  bool isActive; // Whether notification should be shown

  AppNotification() {
    isRead = false;
    isActive = true;
  }
}

enum NotificationPriority {
  low,
  medium,
  high,
  urgent,
}

class NotificationData {
  final String title;
  final String message;
  final String type;
  final NotificationPriority priority;
  final String? relatedId;
  final String? actionType;
  final DateTime? scheduledFor;

  NotificationData({
    required this.title,
    required this.message,
    required this.type,
    this.priority = NotificationPriority.medium,
    this.relatedId,
    this.actionType,
    this.scheduledFor,
  });
}

class NotificationTemplates {
  static NotificationData budgetExceeded(String categoryName, double amount) {
    return NotificationData(
      title: 'تجاوز از بودجه',
      message: 'بودجه دسته‌بندی "$categoryName" به مبلغ ${amount.toStringAsFixed(0)} افغانی تجاوز کرده است.',
      type: 'budget_exceeded',
      priority: NotificationPriority.high,
    );
  }

  static NotificationData budgetWarning(String categoryName, double percentage) {
    return NotificationData(
      title: 'هشدار بودجه',
      message: 'شما ${percentage.toStringAsFixed(0)}% از بودجه "$categoryName" را مصرف کرده‌اید.',
      type: 'budget_warning',
      priority: NotificationPriority.medium,
    );
  }

  static NotificationData loanPaymentReminder(String loanName, String personName) {
    return NotificationData(
      title: 'یادآوری پرداخت وام',
      message: 'فراموش نکنید که قسط وام "$loanName" به $personName را پرداخت کنید.',
      type: 'loan_reminder',
      priority: NotificationPriority.medium,
    );
  }

  static NotificationData monthlyReportReady() {
    return NotificationData(
      title: 'گزارش ماهانه آماده است',
      message: 'گزارش مالی ماه گذشته شما آماده مشاهده است.',
      type: 'monthly_report',
      priority: NotificationPriority.low,
    );
  }

  static NotificationData salaryReminder() {
    return NotificationData(
      title: 'یادآوری ثبت معاش',
      message: 'آیا معاش این ماه را دریافت کرده‌اید؟ فراموش نکنید آن را ثبت کنید.',
      type: 'salary_reminder',
      priority: NotificationPriority.medium,
    );
  }

  static NotificationData financialTip(String tip) {
    return NotificationData(
      title: 'نکته مالی',
      message: tip,
      type: 'financial_tip',
      priority: NotificationPriority.low,
    );
  }

  static NotificationData lowBalance(double balance) {
    return NotificationData(
      title: 'موجودی کم',
      message: 'موجودی شما ${balance.toStringAsFixed(0)} افغانی است. توصیه می‌شود مصارف خود را کنترل کنید.',
      type: 'low_balance',
      priority: NotificationPriority.high,
    );
  }

  static NotificationData goalAchieved(String goalName) {
    return NotificationData(
      title: 'تبریک!',
      message: 'شما به هدف "$goalName" رسیده‌اید!',
      type: 'goal_achieved',
      priority: NotificationPriority.high,
    );
  }
}
