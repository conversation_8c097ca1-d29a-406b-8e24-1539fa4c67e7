import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:my_fincance_app/services/report_service.dart';
import 'package:my_fincance_app/services/transaction_service.dart';
import 'package:my_fincance_app/services/budget_service.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:my_fincance_app/services/category_service.dart';
import 'package:my_fincance_app/utils/currency_formatter.dart';
import 'package:my_fincance_app/utils/date_formatter.dart';
import 'package:my_fincance_app/widgets/custom_button.dart';
import 'package:my_fincance_app/widgets/summary_card.dart';
import 'package:my_fincance_app/widgets/notification_widgets.dart';
import 'package:provider/provider.dart';

class ReportsPage extends StatefulWidget {
  const ReportsPage({super.key});

  @override
  State<ReportsPage> createState() => _ReportsPageState();
}

class _ReportsPageState extends State<ReportsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  DateTime _selectedMonth = DateTime.now();
  int _selectedYear = DateTime.now().year;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('گزارش‌ها'),
        centerTitle: true,
        backgroundColor: Colors.blue.shade50,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.blue,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Colors.blue,
          tabs: const [
            Tab(
              icon: Icon(Icons.calendar_month),
              text: 'ماهانه',
            ),
            Tab(
              icon: Icon(Icons.calendar_today),
              text: 'سالانه',
            ),
            Tab(
              icon: Icon(Icons.account_balance_wallet),
              text: 'وام‌ها',
            ),
          ],
        ),
      ),
      backgroundColor: Colors.grey.shade50,
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMonthlyReportTab(),
          _buildYearlyReportTab(),
          _buildLoanReportTab(),
        ],
      ),
    );
  }

  Widget _buildMonthlyReportTab() {
    return Consumer4<TransactionService, BudgetService, LoanService, CategoryService>(
      builder: (context, transactionService, budgetService, loanService, categoryService, child) {
        final reportService = ReportService(
          transactionService,
          budgetService,
          loanService,
          categoryService,
        );

        final monthlyReport = reportService.generateMonthlyReport(_selectedMonth);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Month selector
              _buildMonthSelector(),
              const SizedBox(height: 20),

              // Summary cards
              _buildMonthlySummaryCards(monthlyReport),
              const SizedBox(height: 20),

              // Category breakdown chart
              _buildCategoryBreakdownChart(monthlyReport),
              const SizedBox(height: 20),

              // Budget performance
              _buildBudgetPerformanceSection(monthlyReport),
              const SizedBox(height: 20),

              // Export button
              _buildExportButton(monthlyReport, reportService),
            ],
          ),
        );
      },
    );
  }

  Widget _buildYearlyReportTab() {
    return Consumer4<TransactionService, BudgetService, LoanService, CategoryService>(
      builder: (context, transactionService, budgetService, loanService, categoryService, child) {
        final reportService = ReportService(
          transactionService,
          budgetService,
          loanService,
          categoryService,
        );

        final yearlyReport = reportService.generateYearlyReport(_selectedYear);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Year selector
              _buildYearSelector(),
              const SizedBox(height: 20),

              // Yearly summary
              _buildYearlySummaryCards(yearlyReport),
              const SizedBox(height: 20),

              // Monthly trend chart
              _buildMonthlyTrendChart(yearlyReport),
              const SizedBox(height: 20),

              // Best/Worst months
              _buildBestWorstMonths(yearlyReport),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoanReportTab() {
    return Consumer4<TransactionService, BudgetService, LoanService, CategoryService>(
      builder: (context, transactionService, budgetService, loanService, categoryService, child) {
        final reportService = ReportService(
          transactionService,
          budgetService,
          loanService,
          categoryService,
        );

        final loanReport = reportService.generateLoanReport();

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Loan summary cards
              _buildLoanSummaryCards(loanReport),
              const SizedBox(height: 20),

              // Loan details list
              _buildLoanDetailsList(loanReport),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMonthSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(Icons.calendar_month, color: Colors.blue),
            const SizedBox(width: 12),
            const Text(
              'انتخاب ماه:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            CustomButton(
              text: DateFormatter.formatPersianMonth(_selectedMonth),
              onPressed: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _selectedMonth,
                  firstDate: DateTime(2020),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() {
                    _selectedMonth = date;
                  });
                }
              },
              isOutlined: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildYearSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(Icons.calendar_today, color: Colors.blue),
            const SizedBox(width: 12),
            const Text(
              'انتخاب سال:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            DropdownButton<int>(
              value: _selectedYear,
              items: List.generate(5, (index) {
                final year = DateTime.now().year - index;
                return DropdownMenuItem(
                  value: year,
                  child: Text(year.toString()),
                );
              }),
              onChanged: (year) {
                if (year != null) {
                  setState(() {
                    _selectedYear = year;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlySummaryCards(MonthlyReport report) {
    return Row(
      children: [
        Expanded(
          child: SummaryCard(
            title: 'مجموع درآمد',
            amount: report.totalIncome,
            icon: Icons.trending_up,
            color: Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: SummaryCard(
            title: 'مجموع مصارف',
            amount: report.totalExpenses,
            icon: Icons.trending_down,
            color: Colors.red,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: SummaryCard(
            title: 'پس‌انداز خالص',
            amount: report.netSavings,
            icon: Icons.savings,
            color: report.netSavings >= 0 ? Colors.green : Colors.red,
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryBreakdownChart(MonthlyReport report) {
    if (report.categoryBreakdown.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(
            child: Text('داده‌ای برای نمایش وجود ندارد'),
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفکیک مصارف بر اساس دسته‌بندی',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: report.categoryBreakdown
                      .where((c) => c.totalAmount > 0)
                      .take(6)
                      .map((category) {
                    final total = report.categoryBreakdown
                        .fold<double>(0.0, (sum, c) => sum + c.totalAmount);
                    final percentage = (category.totalAmount / total) * 100;
                    
                    return PieChartSectionData(
                      value: category.totalAmount,
                      title: '${percentage.toStringAsFixed(1)}%',
                      color: _getCategoryColor(category.categoryId),
                      radius: 60,
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBudgetPerformanceSection(MonthlyReport report) {
    if (report.budgetPerformance.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(
            child: Text('هیچ بودجه‌ای برای این ماه تعریف نشده است'),
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'عملکرد بودجه‌ها',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...report.budgetPerformance.map((performance) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          performance.categoryName,
                          style: const TextStyle(fontWeight: FontWeight.w600),
                        ),
                        Text(
                          '${performance.performancePercentage.toStringAsFixed(1)}%',
                          style: TextStyle(
                            color: performance.isOverBudget ? Colors.red : Colors.green,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: (performance.performancePercentage / 100).clamp(0.0, 1.0),
                      backgroundColor: Colors.grey.shade300,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        performance.isOverBudget ? Colors.red : Colors.green,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'بودجه: ${CurrencyFormatter.format(performance.budget.amount)}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        Text(
                          'مصرف: ${CurrencyFormatter.format(performance.actualSpending)}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildExportButton(MonthlyReport report, ReportService reportService) {
    return SizedBox(
      width: double.infinity,
      child: CustomButton(
        text: 'صادرات گزارش',
        icon: Icons.download,
        onPressed: () {
          final reportText = reportService.exportMonthlyReportAsText(report);
          _showExportDialog(reportText);
        },
      ),
    );
  }

  Color _getCategoryColor(String categoryId) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
    ];
    return colors[categoryId.hashCode % colors.length];
  }

  void _showExportDialog(String reportText) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('گزارش صادر شده'),
        content: SingleChildScrollView(
          child: Text(reportText),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('بستن'),
          ),
          ElevatedButton(
            onPressed: () {
              // Here you could implement actual file export
              CustomSnackBar.show(
                context,
                message: 'گزارش کپی شد',
                type: NotificationType.success,
              );
              Navigator.pop(context);
            },
            child: const Text('کپی'),
          ),
        ],
      ),
    );
  }

  // Placeholder methods for yearly and loan reports
  Widget _buildYearlySummaryCards(YearlyReport report) {
    return const Placeholder(fallbackHeight: 100);
  }

  Widget _buildMonthlyTrendChart(YearlyReport report) {
    return const Placeholder(fallbackHeight: 200);
  }

  Widget _buildBestWorstMonths(YearlyReport report) {
    return const Placeholder(fallbackHeight: 100);
  }

  Widget _buildLoanSummaryCards(LoanReport report) {
    return const Placeholder(fallbackHeight: 100);
  }

  Widget _buildLoanDetailsList(LoanReport report) {
    return const Placeholder(fallbackHeight: 200);
  }
}
