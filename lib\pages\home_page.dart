import 'package:flutter/material.dart';
import 'package:my_fincance_app/pages/dashboard_page.dart';
import 'package:my_fincance_app/pages/loans_page.dart';
import 'package:my_fincance_app/pages/notifications_page.dart';
import 'package:my_fincance_app/pages/reports_page.dart';
import 'package:my_fincance_app/pages/settings_page.dart';
import 'package:my_fincance_app/pages/transactions_page.dart';
import 'package:my_fincance_app/pages/budgets_page.dart';
import 'package:my_fincance_app/services/notification_service.dart';
import 'package:provider/provider.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;

  static const List<Widget> _widgetOptions = <Widget>[
    DashboardPage(),
    LoansPage(),
    TransactionsPage(),
    BudgetsPage(),
    ReportsPage(),
    SettingsPage(),
  ];



  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _selectedIndex == 0 ? _buildDashboardAppBar(context) : null,
      body: _widgetOptions.elementAt(_selectedIndex),
      bottomNavigationBar: BottomNavigationBar(
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'داشبورد',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_balance_wallet),
            label: 'وام‌ها',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.transform),
            label: 'تراکنش‌ها',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.pie_chart),
            label: 'بودجه‌بندی',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.assessment),
            label: 'گزارش‌ها',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'تنظیمات',
          ),
        ],
        currentIndex: _selectedIndex,
        selectedItemColor: Colors.blue,
        unselectedItemColor: Colors.grey,
        type: BottomNavigationBarType.fixed,
        onTap: _onItemTapped,
      ),
    );
  }

  AppBar? _buildDashboardAppBar(BuildContext context) {
    return AppBar(
      title: const Text('مرکز مالی من'),
      centerTitle: true,
      backgroundColor: Colors.blue.shade50,
      elevation: 0,
      actions: [
        Consumer<NotificationService>(
          builder: (context, notificationService, child) {
            final unreadCount = notificationService.unreadCount;
            return Stack(
              children: [
                IconButton(
                  icon: const Icon(Icons.notifications),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const NotificationsPage(),
                      ),
                    );
                  },
                ),
                if (unreadCount > 0)
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        unreadCount > 99 ? '99+' : unreadCount.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ],
    );
  }
}
