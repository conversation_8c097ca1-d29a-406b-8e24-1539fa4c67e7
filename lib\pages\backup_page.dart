import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:my_fincance_app/services/backup_service.dart';
import 'package:my_fincance_app/services/auth_service.dart';
import 'package:my_fincance_app/widgets/custom_button.dart';
import 'package:my_fincance_app/widgets/notification_widgets.dart';
import 'package:my_fincance_app/widgets/empty_state.dart';
import 'package:provider/provider.dart';

class BackupPage extends StatefulWidget {
  const BackupPage({super.key});

  @override
  State<BackupPage> createState() => _BackupPageState();
}

class _BackupPageState extends State<BackupPage> {
  bool _isCreatingBackup = false;
  bool _isRestoringBackup = false;
  BackupStats? _backupStats;
  final TextEditingController _restoreController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadBackupStats();
  }

  @override
  void dispose() {
    _restoreController.dispose();
    super.dispose();
  }

  Future<void> _loadBackupStats() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    final backupService = BackupService(authService.currentUserId);
    
    try {
      final stats = await backupService.getBackupStats();
      setState(() {
        _backupStats = stats;
      });
    } catch (e) {
      if (mounted) {
        CustomSnackBar.show(
          context,
          message: 'خطا در بارگذاری آمار پشتیبان‌گیری',
          type: NotificationType.error,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('پشتیبان‌گیری و بازیابی'),
        centerTitle: true,
        backgroundColor: Colors.blue.shade50,
        elevation: 0,
      ),
      backgroundColor: Colors.grey.shade50,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(),
            const SizedBox(height: 20),
            _buildStatsCard(),
            const SizedBox(height: 20),
            _buildBackupSection(),
            const SizedBox(height: 20),
            _buildRestoreSection(),
            const SizedBox(height: 20),
            _buildWarningCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue.shade600),
                const SizedBox(width: 8),
                const Text(
                  'درباره پشتیبان‌گیری',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'پشتیبان‌گیری شامل تمام اطلاعات مالی شما می‌شود:\n'
              '• تراکنش‌ها\n'
              '• دسته‌بندی‌ها\n'
              '• بودجه‌ها\n'
              '• وام‌ها\n'
              '• اعلان‌ها\n\n'
              'توصیه می‌شود به طور منظم از اطلاعات خود پشتیبان تهیه کنید.',
              style: TextStyle(
                fontSize: 14,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard() {
    if (_backupStats == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'آمار اطلاعات شما',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'تراکنش‌ها',
                    _backupStats!.transactionCount.toString(),
                    Icons.receipt_long,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'دسته‌بندی‌ها',
                    _backupStats!.categoryCount.toString(),
                    Icons.category,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'بودجه‌ها',
                    _backupStats!.budgetCount.toString(),
                    Icons.pie_chart,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'وام‌ها',
                    _backupStats!.loanCount.toString(),
                    Icons.account_balance_wallet,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBackupSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ایجاد پشتیبان',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'پشتیبانی از تمام اطلاعات مالی شما ایجاد کنید. این فایل را در مکان امنی ذخیره کنید.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: CustomButton(
                text: 'ایجاد پشتیبان',
                icon: Icons.backup,
                onPressed: _isCreatingBackup ? null : _createBackup,
                isLoading: _isCreatingBackup,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRestoreSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'بازیابی از پشتیبان',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'محتوای فایل پشتیبان را در کادر زیر قرار دهید:',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _restoreController,
              maxLines: 6,
              decoration: const InputDecoration(
                hintText: 'محتوای فایل پشتیبان را اینجا قرار دهید...',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'قرار دادن از کلیپ‌بورد',
                    icon: Icons.content_paste,
                    onPressed: _pasteFromClipboard,
                    isOutlined: true,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: CustomButton(
                    text: 'بازیابی',
                    icon: Icons.restore,
                    onPressed: _isRestoringBackup || _restoreController.text.isEmpty
                        ? null
                        : _restoreBackup,
                    isLoading: _isRestoringBackup,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarningCard() {
    return Card(
      color: Colors.orange.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: Colors.orange.shade700),
                const SizedBox(width: 8),
                Text(
                  'هشدار مهم',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '• بازیابی از پشتیبان تمام اطلاعات فعلی شما را جایگزین می‌کند\n'
              '• قبل از بازیابی، حتماً از اطلاعات فعلی پشتیبان تهیه کنید\n'
              '• فایل پشتیبان حاوی اطلاعات حساس است، آن را در مکان امنی نگهداری کنید\n'
              '• هرگز فایل پشتیبان خود را با دیگران به اشتراک نگذارید',
              style: TextStyle(
                fontSize: 14,
                color: Colors.orange.shade800,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _createBackup() async {
    setState(() {
      _isCreatingBackup = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final backupService = BackupService(authService.currentUserId);
      
      final backupJson = await backupService.exportBackupAsJson();
      
      // Copy to clipboard
      await Clipboard.setData(ClipboardData(text: backupJson));
      
      if (mounted) {
        CustomSnackBar.show(
          context,
          message: 'پشتیبان ایجاد شد و در کلیپ‌بورد کپی شد',
          type: NotificationType.success,
          duration: const Duration(seconds: 5),
        );
        
        // Show backup content dialog
        _showBackupDialog(backupJson);
      }
    } catch (e) {
      if (mounted) {
        CustomSnackBar.show(
          context,
          message: 'خطا در ایجاد پشتیبان: ${e.toString()}',
          type: NotificationType.error,
        );
      }
    } finally {
      setState(() {
        _isCreatingBackup = false;
      });
    }
  }

  Future<void> _restoreBackup() async {
    final confirmed = await ConfirmationDialog.show(
      context,
      title: 'تأیید بازیابی',
      message: 'آیا مطمئن هستید؟ این عمل تمام اطلاعات فعلی شما را جایگزین می‌کند.',
      confirmText: 'بازیابی',
      cancelText: 'لغو',
      type: NotificationType.warning,
    );

    if (confirmed != true) return;

    setState(() {
      _isRestoringBackup = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final backupService = BackupService(authService.currentUserId);
      
      await backupService.restoreFromJson(_restoreController.text);
      
      if (mounted) {
        CustomSnackBar.show(
          context,
          message: 'اطلاعات با موفقیت بازیابی شد',
          type: NotificationType.success,
        );
        
        _restoreController.clear();
        _loadBackupStats();
      }
    } catch (e) {
      if (mounted) {
        CustomSnackBar.show(
          context,
          message: 'خطا در بازیابی: ${e.toString()}',
          type: NotificationType.error,
        );
      }
    } finally {
      setState(() {
        _isRestoringBackup = false;
      });
    }
  }

  Future<void> _pasteFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null) {
        setState(() {
          _restoreController.text = clipboardData!.text!;
        });
        
        CustomSnackBar.show(
          context,
          message: 'محتوا از کلیپ‌بورد قرار داده شد',
          type: NotificationType.success,
        );
      } else {
        CustomSnackBar.show(
          context,
          message: 'کلیپ‌بورد خالی است',
          type: NotificationType.warning,
        );
      }
    } catch (e) {
      CustomSnackBar.show(
        context,
        message: 'خطا در خواندن کلیپ‌بورد',
        type: NotificationType.error,
      );
    }
  }

  void _showBackupDialog(String backupJson) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('پشتیبان ایجاد شد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('پشتیبان شما آماده است. این محتوا در کلیپ‌بورد کپی شده است:'),
            const SizedBox(height: 12),
            Container(
              height: 200,
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
              child: SingleChildScrollView(
                child: Text(
                  backupJson,
                  style: const TextStyle(
                    fontSize: 10,
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('بستن'),
          ),
          ElevatedButton(
            onPressed: () async {
              await Clipboard.setData(ClipboardData(text: backupJson));
              Navigator.pop(context);
              CustomSnackBar.show(
                context,
                message: 'دوباره در کلیپ‌بورد کپی شد',
                type: NotificationType.success,
              );
            },
            child: const Text('کپی مجدد'),
          ),
        ],
      ),
    );
  }
}
