import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:my_fincance_app/widgets/notification_widgets.dart';

enum ErrorType {
  network,
  validation,
  storage,
  authentication,
  permission,
  unknown,
}

class AppError {
  final String message;
  final ErrorType type;
  final String? details;
  final DateTime timestamp;
  final String? stackTrace;

  AppError({
    required this.message,
    required this.type,
    this.details,
    this.stackTrace,
  }) : timestamp = DateTime.now();

  @override
  String toString() {
    return 'AppError: $message (Type: $type, Time: $timestamp)';
  }
}

class ErrorService extends ChangeNotifier {
  final List<AppError> _errors = [];
  
  List<AppError> get errors => List.unmodifiable(_errors);
  List<AppError> get recentErrors => _errors
      .where((error) => error.timestamp.isAfter(
          DateTime.now().subtract(const Duration(hours: 24))))
      .toList();

  void logError(AppError error) {
    _errors.add(error);
    
    // Keep only last 100 errors
    if (_errors.length > 100) {
      _errors.removeAt(0);
    }
    
    // Log to console in debug mode
    if (kDebugMode) {
      debugPrint('Error logged: ${error.toString()}');
      if (error.details != null) {
        debugPrint('Details: ${error.details}');
      }
      if (error.stackTrace != null) {
        debugPrint('Stack trace: ${error.stackTrace}');
      }
    }
    
    notifyListeners();
  }

  void clearErrors() {
    _errors.clear();
    notifyListeners();
  }

  void clearOldErrors({Duration maxAge = const Duration(days: 7)}) {
    final cutoffTime = DateTime.now().subtract(maxAge);
    _errors.removeWhere((error) => error.timestamp.isBefore(cutoffTime));
    notifyListeners();
  }

  // Helper methods for different error types
  void logNetworkError(String message, {String? details}) {
    logError(AppError(
      message: message,
      type: ErrorType.network,
      details: details,
    ));
  }

  void logValidationError(String message, {String? details}) {
    logError(AppError(
      message: message,
      type: ErrorType.validation,
      details: details,
    ));
  }

  void logStorageError(String message, {String? details}) {
    logError(AppError(
      message: message,
      type: ErrorType.storage,
      details: details,
    ));
  }

  void logAuthenticationError(String message, {String? details}) {
    logError(AppError(
      message: message,
      type: ErrorType.authentication,
      details: details,
    ));
  }

  void logUnknownError(String message, {String? details, String? stackTrace}) {
    logError(AppError(
      message: message,
      type: ErrorType.unknown,
      details: details,
      stackTrace: stackTrace,
    ));
  }

  // Show error to user with appropriate UI
  static void showError(BuildContext context, AppError error) {
    NotificationType notificationType;
    
    switch (error.type) {
      case ErrorType.network:
        notificationType = NotificationType.warning;
        break;
      case ErrorType.validation:
        notificationType = NotificationType.info;
        break;
      case ErrorType.authentication:
      case ErrorType.permission:
        notificationType = NotificationType.error;
        break;
      default:
        notificationType = NotificationType.error;
        break;
    }

    CustomSnackBar.show(
      context,
      message: error.message,
      type: notificationType,
      duration: const Duration(seconds: 4),
    );
  }

  // Show error with user-friendly message
  static void showUserFriendlyError(BuildContext context, String technicalMessage) {
    String userMessage;
    NotificationType type;

    if (technicalMessage.toLowerCase().contains('network') ||
        technicalMessage.toLowerCase().contains('connection')) {
      userMessage = 'مشکل در اتصال به اینترنت. لطفاً اتصال خود را بررسی کنید.';
      type = NotificationType.warning;
    } else if (technicalMessage.toLowerCase().contains('timeout')) {
      userMessage = 'عملیات خیلی طول کشید. لطفاً دوباره تلاش کنید.';
      type = NotificationType.warning;
    } else if (technicalMessage.toLowerCase().contains('permission') ||
               technicalMessage.toLowerCase().contains('unauthorized')) {
      userMessage = 'شما مجوز انجام این عملیات را ندارید.';
      type = NotificationType.error;
    } else if (technicalMessage.toLowerCase().contains('storage') ||
               technicalMessage.toLowerCase().contains('database')) {
      userMessage = 'مشکل در ذخیره‌سازی اطلاعات. لطفاً دوباره تلاش کنید.';
      type = NotificationType.error;
    } else {
      userMessage = 'خطای غیرمنتظره‌ای رخ داد. لطفاً دوباره تلاش کنید.';
      type = NotificationType.error;
    }

    CustomSnackBar.show(
      context,
      message: userMessage,
      type: type,
      duration: const Duration(seconds: 4),
    );
  }

  // Handle exceptions and convert to AppError
  static AppError handleException(Exception exception, {String? context}) {
    String message;
    ErrorType type;
    String? details;

    if (exception is FormatException) {
      message = 'فرمت داده‌ها صحیح نیست';
      type = ErrorType.validation;
      details = exception.message;
    } else if (exception is ArgumentError) {
      message = 'پارامترهای ورودی صحیح نیستند';
      type = ErrorType.validation;
      details = exception.message;
    } else if (exception.toString().contains('SocketException') ||
               exception.toString().contains('HttpException')) {
      message = 'مشکل در اتصال به شبکه';
      type = ErrorType.network;
      details = exception.toString();
    } else {
      message = context != null ? 'خطا در $context' : 'خطای غیرمنتظره';
      type = ErrorType.unknown;
      details = exception.toString();
    }

    return AppError(
      message: message,
      type: type,
      details: details,
      stackTrace: StackTrace.current.toString(),
    );
  }

  // Retry mechanism for operations
  static Future<T> retryOperation<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
    bool Function(Exception)? shouldRetry,
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempts++;
        
        if (attempts >= maxRetries) {
          rethrow;
        }
        
        if (shouldRetry != null && e is Exception && !shouldRetry(e)) {
          rethrow;
        }
        
        await Future.delayed(delay * attempts);
      }
    }
    
    throw Exception('Max retries exceeded');
  }

  // Safe operation wrapper
  static Future<T?> safeOperation<T>(
    Future<T> Function() operation, {
    required BuildContext context,
    String? errorContext,
    bool showErrorToUser = true,
  }) async {
    try {
      return await operation();
    } catch (e) {
      final error = e is Exception 
          ? handleException(e, context: errorContext)
          : AppError(
              message: errorContext != null ? 'خطا در $errorContext' : 'خطای غیرمنتظره',
              type: ErrorType.unknown,
              details: e.toString(),
            );

      if (showErrorToUser) {
        showError(context, error);
      }

      return null;
    }
  }

  // Validation helper
  static bool validateAndShowErrors(
    BuildContext context,
    Map<String, String? Function()> validations,
  ) {
    final errors = <String>[];
    
    for (final entry in validations.entries) {
      final error = entry.value();
      if (error != null) {
        errors.add('${entry.key}: $error');
      }
    }
    
    if (errors.isNotEmpty) {
      CustomSnackBar.show(
        context,
        message: errors.first,
        type: NotificationType.warning,
      );
      return false;
    }
    
    return true;
  }
}
