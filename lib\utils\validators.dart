class Validators {
  // Email validation
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'ایمیل الزامی است';
    }
    
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(value)) {
      return 'فرمت ایمیل صحیح نیست';
    }
    
    return null;
  }

  // Password validation
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'رمز عبور الزامی است';
    }
    
    if (value.length < 6) {
      return 'رمز عبور باید حداقل ۶ کاراکتر باشد';
    }
    
    return null;
  }

  // Name validation
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'نام الزامی است';
    }
    
    if (value.trim().length < 2) {
      return 'نام باید حداقل ۲ کاراکتر باشد';
    }
    
    return null;
  }

  // Amount validation
  static String? validateAmount(String? value) {
    if (value == null || value.isEmpty) {
      return 'مبلغ الزامی است';
    }
    
    final amount = double.tryParse(value.replaceAll(',', ''));
    if (amount == null) {
      return 'مبلغ وارد شده صحیح نیست';
    }
    
    if (amount <= 0) {
      return 'مبلغ باید بزرگتر از صفر باشد';
    }
    
    if (amount > 999999999) {
      return 'مبلغ خیلی زیاد است';
    }
    
    return null;
  }

  // Positive amount validation (for budgets, loans, etc.)
  static String? validatePositiveAmount(String? value) {
    final result = validateAmount(value);
    if (result != null) return result;
    
    final amount = double.parse(value!.replaceAll(',', ''));
    if (amount <= 0) {
      return 'مبلغ باید مثبت باشد';
    }
    
    return null;
  }

  // Description validation
  static String? validateDescription(String? value) {
    if (value == null || value.isEmpty) {
      return 'توضیحات الزامی است';
    }
    
    if (value.trim().length < 3) {
      return 'توضیحات باید حداقل ۳ کاراکتر باشد';
    }
    
    if (value.length > 200) {
      return 'توضیحات نباید بیش از ۲۰۰ کاراکتر باشد';
    }
    
    return null;
  }

  // Required field validation
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName الزامی است';
    }
    return null;
  }

  // Phone number validation (Afghan format)
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Phone is optional
    }
    
    // Remove spaces and special characters
    final cleanPhone = value.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Afghan phone number patterns
    final afghanPatterns = [
      RegExp(r'^\+93[0-9]{9}$'), // +93xxxxxxxxx
      RegExp(r'^0[0-9]{9}$'),    // 0xxxxxxxxx
      RegExp(r'^[0-9]{9}$'),     // xxxxxxxxx
    ];
    
    bool isValid = afghanPatterns.any((pattern) => pattern.hasMatch(cleanPhone));
    
    if (!isValid) {
      return 'شماره تلفن صحیح نیست';
    }
    
    return null;
  }

  // Date validation
  static String? validateDate(DateTime? value) {
    if (value == null) {
      return 'تاریخ الزامی است';
    }
    
    final now = DateTime.now();
    final maxDate = DateTime(now.year + 10);
    final minDate = DateTime(2000);
    
    if (value.isAfter(maxDate)) {
      return 'تاریخ خیلی دور در آینده است';
    }
    
    if (value.isBefore(minDate)) {
      return 'تاریخ خیلی قدیمی است';
    }
    
    return null;
  }

  // Future date validation (for loan start dates, etc.)
  static String? validateFutureDate(DateTime? value, {bool allowToday = true}) {
    final basicValidation = validateDate(value);
    if (basicValidation != null) return basicValidation;
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDate = DateTime(value!.year, value.month, value.day);
    
    if (allowToday) {
      if (selectedDate.isBefore(today)) {
        return 'تاریخ نمی‌تواند در گذشته باشد';
      }
    } else {
      if (selectedDate.isBefore(today.add(const Duration(days: 1)))) {
        return 'تاریخ باید در آینده باشد';
      }
    }
    
    return null;
  }

  // Past date validation (for transaction dates, etc.)
  static String? validatePastDate(DateTime? value, {bool allowToday = true}) {
    final basicValidation = validateDate(value);
    if (basicValidation != null) return basicValidation;
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDate = DateTime(value!.year, value.month, value.day);
    
    if (allowToday) {
      if (selectedDate.isAfter(today)) {
        return 'تاریخ نمی‌تواند در آینده باشد';
      }
    } else {
      if (selectedDate.isAfter(today.subtract(const Duration(days: 1)))) {
        return 'تاریخ باید در گذشته باشد';
      }
    }
    
    return null;
  }

  // Loan amount validation (considering user's financial capacity)
  static String? validateLoanAmount(String? value, double userCapital) {
    final basicValidation = validatePositiveAmount(value);
    if (basicValidation != null) return basicValidation;
    
    final amount = double.parse(value!.replaceAll(',', ''));
    
    // Warning for large loan amounts
    if (amount > userCapital * 10) {
      return 'مبلغ وام بسیار زیاد است نسبت به سرمایه شما';
    }
    
    return null;
  }

  // Budget amount validation
  static String? validateBudgetAmount(String? value, double monthlyIncome) {
    final basicValidation = validatePositiveAmount(value);
    if (basicValidation != null) return basicValidation;
    
    final amount = double.parse(value!.replaceAll(',', ''));
    
    // Warning for unrealistic budget amounts
    if (amount > monthlyIncome) {
      return 'بودجه بیش از درآمد ماهانه شما است';
    }
    
    return null;
  }

  // Category name validation
  static String? validateCategoryName(String? value, List<String> existingNames) {
    if (value == null || value.isEmpty) {
      return 'نام دسته‌بندی الزامی است';
    }
    
    if (value.trim().length < 2) {
      return 'نام دسته‌بندی باید حداقل ۲ کاراکتر باشد';
    }
    
    if (value.length > 50) {
      return 'نام دسته‌بندی نباید بیش از ۵۰ کاراکتر باشد';
    }
    
    if (existingNames.contains(value.trim())) {
      return 'این نام دسته‌بندی قبلاً استفاده شده است';
    }
    
    return null;
  }

  // Loan name validation
  static String? validateLoanName(String? value, List<String> existingNames) {
    if (value == null || value.isEmpty) {
      return 'نام وام الزامی است';
    }
    
    if (value.trim().length < 2) {
      return 'نام وام باید حداقل ۲ کاراکتر باشد';
    }
    
    if (value.length > 100) {
      return 'نام وام نباید بیش از ۱۰۰ کاراکتر باشد';
    }
    
    if (existingNames.contains(value.trim())) {
      return 'این نام وام قبلاً استفاده شده است';
    }
    
    return null;
  }

  // Person name validation (for loans)
  static String? validatePersonName(String? value) {
    if (value == null || value.isEmpty) {
      return 'نام شخص الزامی است';
    }
    
    if (value.trim().length < 2) {
      return 'نام شخص باید حداقل ۲ کاراکتر باشد';
    }
    
    if (value.length > 100) {
      return 'نام شخص نباید بیش از ۱۰۰ کاراکتر باشد';
    }
    
    return null;
  }

  // Confirm password validation
  static String? validateConfirmPassword(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'تأیید رمز عبور الزامی است';
    }
    
    if (value != password) {
      return 'رمز عبور و تأیید آن یکسان نیستند';
    }
    
    return null;
  }
}
