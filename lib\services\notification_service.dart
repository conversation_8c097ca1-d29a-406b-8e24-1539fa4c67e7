import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:my_fincance_app/models/notification.dart';
import 'package:my_fincance_app/services/budget_service.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:my_fincance_app/services/transaction_service.dart';
import 'package:uuid/uuid.dart';

class NotificationService extends ChangeNotifier {
  final Box<AppNotification> _notificationBox = Hive.box<AppNotification>('notifications');
  final String _userId;
  final _uuid = Uuid();

  List<AppNotification> _notifications = [];

  NotificationService(this._userId) {
    _loadNotifications();
  }

  List<AppNotification> get notifications => _notifications;
  List<AppNotification> get unreadNotifications => 
      _notifications.where((n) => !n.isRead && n.isActive).toList();
  
  int get unreadCount => unreadNotifications.length;

  void _loadNotifications() {
    _notifications = _notificationBox.values
        .where((n) => n.userId == _userId)
        .toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    notifyListeners();
  }

  void addNotification(NotificationData data) {
    final notification = AppNotification()
      ..id = _uuid.v4()
      ..title = data.title
      ..message = data.message
      ..type = data.type
      ..createdAt = DateTime.now()
      ..userId = _userId
      ..relatedId = data.relatedId
      ..actionType = data.actionType
      ..scheduledFor = data.scheduledFor;

    _notificationBox.put(notification.id, notification);
    _loadNotifications();
  }

  void markAsRead(String notificationId) {
    final notification = _notificationBox.get(notificationId);
    if (notification != null) {
      notification.isRead = true;
      notification.save();
      _loadNotifications();
    }
  }

  void markAllAsRead() {
    for (final notification in unreadNotifications) {
      notification.isRead = true;
      notification.save();
    }
    _loadNotifications();
  }

  void deleteNotification(String notificationId) {
    final notification = _notificationBox.get(notificationId);
    if (notification != null) {
      notification.delete();
      _loadNotifications();
    }
  }

  void clearOldNotifications({int daysToKeep = 30}) {
    final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
    final oldNotifications = _notifications
        .where((n) => n.createdAt.isBefore(cutoffDate))
        .toList();

    for (final notification in oldNotifications) {
      notification.delete();
    }
    _loadNotifications();
  }

  // Check for budget-related notifications
  void checkBudgetNotifications(BudgetService budgetService) {
    final currentMonthBudgets = budgetService.currentMonthBudgets;
    
    for (final budget in currentMonthBudgets) {
      final progress = budgetService.getBudgetProgress(budget);
      final isExceeded = budgetService.isBudgetExceeded(budget);
      
      // Check if we already sent a notification for this budget this month
      final existingNotification = _notifications.any((n) =>
        n.type == 'budget_exceeded' &&
        n.relatedId == budget.id &&
        n.createdAt.month == DateTime.now().month &&
        n.createdAt.year == DateTime.now().year
      );

      if (isExceeded && !existingNotification) {
        final categoryName = budget.categoryId; // You might want to get actual category name
        final excessAmount = budgetService.getActualSpending(budget.categoryId, budget.period) - budget.amount;
        addNotification(NotificationTemplates.budgetExceeded(categoryName, excessAmount));
      } else if (progress >= 0.8 && progress < 1.0 && !existingNotification) {
        final categoryName = budget.categoryId;
        addNotification(NotificationTemplates.budgetWarning(categoryName, progress * 100));
      }
    }
  }

  // Check for loan-related notifications
  void checkLoanNotifications(LoanService loanService) {
    final activeLoans = loanService.activeLoans;
    
    for (final loan in activeLoans) {
      // Check if it's been a while since last payment
      final lastPaymentDate = _getLastPaymentDate(loan.id);
      if (lastPaymentDate != null) {
        final daysSinceLastPayment = DateTime.now().difference(lastPaymentDate).inDays;
        
        // Send reminder if no payment for 30 days
        if (daysSinceLastPayment >= 30) {
          final existingReminder = _notifications.any((n) =>
            n.type == 'loan_reminder' &&
            n.relatedId == loan.id &&
            n.createdAt.isAfter(DateTime.now().subtract(const Duration(days: 7)))
          );
          
          if (!existingReminder) {
            addNotification(NotificationTemplates.loanPaymentReminder(loan.name, loan.person));
          }
        }
      }
    }
  }

  // Check for salary reminder
  void checkSalaryReminder(TransactionService transactionService) {
    final now = DateTime.now();
    final isAfter15th = now.day >= 15;
    
    if (isAfter15th) {
      final salaryThisMonth = transactionService.currentMonthTransactions.any((t) =>
        t.type == 'درآمد' && 
        (t.description.toLowerCase().contains('معاش') ||
         t.description.toLowerCase().contains('حقوق') ||
         t.description.toLowerCase().contains('salary'))
      );
      
      if (!salaryThisMonth) {
        final existingReminder = _notifications.any((n) =>
          n.type == 'salary_reminder' &&
          n.createdAt.month == now.month &&
          n.createdAt.year == now.year
        );
        
        if (!existingReminder) {
          addNotification(NotificationTemplates.salaryReminder());
        }
      }
    }
  }

  // Check for low balance warning
  void checkLowBalance(TransactionService transactionService) {
    final balance = transactionService.totalCapital;
    
    if (balance < 10000) { // Less than 10,000 AFN
      final existingWarning = _notifications.any((n) =>
        n.type == 'low_balance' &&
        n.createdAt.isAfter(DateTime.now().subtract(const Duration(days: 3)))
      );
      
      if (!existingWarning) {
        addNotification(NotificationTemplates.lowBalance(balance));
      }
    }
  }

  // Generate monthly financial tips
  void generateFinancialTips() {
    final tips = [
      'سعی کنید هر ماه حداقل ۲۰٪ از درآمد خود را پس‌انداز کنید.',
      'مصارف غیرضروری خود را شناسایی و کاهش دهید.',
      'برای خریدهای بزرگ برنامه‌ریزی کنید و از خرید آنی خودداری کنید.',
      'بدهی‌های خود را در اولویت قرار دهید و سعی کنید زودتر پرداخت کنید.',
      'یک صندوق اضطراری برای شرایط غیرمترقبه ایجاد کنید.',
    ];
    
    final randomTip = tips[DateTime.now().day % tips.length];
    
    final existingTip = _notifications.any((n) =>
      n.type == 'financial_tip' &&
      n.createdAt.isAfter(DateTime.now().subtract(const Duration(days: 7)))
    );
    
    if (!existingTip) {
      addNotification(NotificationTemplates.financialTip(randomTip));
    }
  }

  // Run all notification checks
  void runNotificationChecks(
    BudgetService budgetService,
    LoanService loanService,
    TransactionService transactionService,
  ) {
    checkBudgetNotifications(budgetService);
    checkLoanNotifications(loanService);
    checkSalaryReminder(transactionService);
    checkLowBalance(transactionService);
    generateFinancialTips();
  }

  DateTime? _getLastPaymentDate(String loanId) {
    // This would need to be implemented by checking transaction service
    // for the last transaction related to this loan
    return null; // Placeholder
  }
}
