import 'package:flutter/material.dart';
import 'package:my_fincance_app/models/notification.dart';
import 'package:my_fincance_app/services/notification_service.dart';
import 'package:my_fincance_app/widgets/empty_state.dart';
import 'package:my_fincance_app/widgets/notification_widgets.dart';
import 'package:provider/provider.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اعلان‌ها'),
        centerTitle: true,
        backgroundColor: Colors.blue.shade50,
        elevation: 0,
        actions: [
          Consumer<NotificationService>(
            builder: (context, notificationService, child) {
              return PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'mark_all_read':
                      notificationService.markAllAsRead();
                      CustomSnackBar.show(
                        context,
                        message: 'همه اعلان‌ها به عنوان خوانده شده علامت‌گذاری شدند',
                        type: NotificationType.success,
                      );
                      break;
                    case 'clear_old':
                      notificationService.clearOldNotifications();
                      CustomSnackBar.show(
                        context,
                        message: 'اعلان‌های قدیمی پاک شدند',
                        type: NotificationType.success,
                      );
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'mark_all_read',
                    child: Row(
                      children: [
                        Icon(Icons.mark_email_read),
                        SizedBox(width: 8),
                        Text('علامت‌گذاری همه به عنوان خوانده شده'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'clear_old',
                    child: Row(
                      children: [
                        Icon(Icons.clear_all),
                        SizedBox(width: 8),
                        Text('پاک کردن اعلان‌های قدیمی'),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.blue,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Colors.blue,
          tabs: [
            Consumer<NotificationService>(
              builder: (context, notificationService, child) {
                final unreadCount = notificationService.unreadCount;
                return Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.notifications),
                      const SizedBox(width: 8),
                      const Text('جدید'),
                      if (unreadCount > 0) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            unreadCount.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              },
            ),
            const Tab(
              icon: Icon(Icons.history),
              text: 'همه',
            ),
          ],
        ),
      ),
      backgroundColor: Colors.grey.shade50,
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildUnreadNotifications(),
          _buildAllNotifications(),
        ],
      ),
    );
  }

  Widget _buildUnreadNotifications() {
    return Consumer<NotificationService>(
      builder: (context, notificationService, child) {
        final unreadNotifications = notificationService.unreadNotifications;

        if (unreadNotifications.isEmpty) {
          return const EmptyState(
            icon: Icons.notifications_none,
            title: 'اعلان جدیدی وجود ندارد',
            subtitle: 'همه اعلان‌های شما خوانده شده‌اند',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: unreadNotifications.length,
          itemBuilder: (context, index) {
            final notification = unreadNotifications[index];
            return _buildNotificationCard(notification, notificationService);
          },
        );
      },
    );
  }

  Widget _buildAllNotifications() {
    return Consumer<NotificationService>(
      builder: (context, notificationService, child) {
        final allNotifications = notificationService.notifications;

        if (allNotifications.isEmpty) {
          return const EmptyState(
            icon: Icons.notifications_none,
            title: 'هیچ اعلانی وجود ندارد',
            subtitle: 'اعلان‌های مالی شما در اینجا نمایش داده می‌شوند',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: allNotifications.length,
          itemBuilder: (context, index) {
            final notification = allNotifications[index];
            return _buildNotificationCard(notification, notificationService);
          },
        );
      },
    );
  }

  Widget _buildNotificationCard(
    AppNotification notification,
    NotificationService notificationService,
  ) {
    NotificationType type;
    switch (notification.type) {
      case 'budget_exceeded':
      case 'low_balance':
        type = NotificationType.error;
        break;
      case 'budget_warning':
      case 'loan_reminder':
      case 'salary_reminder':
        type = NotificationType.warning;
        break;
      case 'goal_achieved':
      case 'monthly_report':
        type = NotificationType.success;
        break;
      default:
        type = NotificationType.info;
        break;
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Opacity(
        opacity: notification.isRead ? 0.7 : 1.0,
        child: NotificationCard(
          title: notification.title,
          message: notification.message,
          type: type,
          timestamp: notification.createdAt,
          onTap: () {
            if (!notification.isRead) {
              notificationService.markAsRead(notification.id);
            }
            _handleNotificationAction(notification);
          },
          onDismiss: () async {
            final confirmed = await ConfirmationDialog.show(
              context,
              title: 'حذف اعلان',
              message: 'آیا مطمئن هستید که می‌خواهید این اعلان را حذف کنید؟',
              confirmText: 'حذف',
              cancelText: 'لغو',
              type: NotificationType.warning,
            );

            if (confirmed == true) {
              notificationService.deleteNotification(notification.id);
              CustomSnackBar.show(
                context,
                message: 'اعلان حذف شد',
                type: NotificationType.success,
              );
            }
          },
        ),
      ),
    );
  }

  void _handleNotificationAction(AppNotification notification) {
    // Handle different notification actions
    switch (notification.actionType) {
      case 'view_loan':
        // Navigate to loan details
        break;
      case 'view_budget':
        // Navigate to budget page
        break;
      case 'add_transaction':
        // Navigate to add transaction
        break;
      default:
        // Show notification details or do nothing
        break;
    }
  }
}
